"""CodeOCR API server for converting code screenshots to editable code."""

import asyncio
import json
import os
from pathlib import Path

import instructor
import logfire
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator

load_dotenv(dotenv_path=".env.development", override=True)


class ImageInput(BaseModel):
    """Input model for image data supporting multiple image source formats.

    This model validates and structures the incoming image data for processing
    by the code extraction service. The source field supports various formats
    that can be automatically detected by the instructor library.

    Attributes:
        source: Image source that can be a URL, file path, or base64-encoded string.
                The instructor library's Image.autodetect() method automatically
                determines the format and processes accordingly.
    """

    source: str | Path = Field(
        ...,
        description=(
            "Image source supporting URL, file path, or base64-encoded content "
            "for code extraction."
        ),
    )


class CodeOCRResponse(BaseModel):
    """Response model containing detected programming language and extracted code.

    This model structures the output from the code extraction service, providing
    both the extracted code content and the automatically detected or specified
    programming language.

    Attributes:
        language: The detected or specified programming language of the extracted code.
        code: The code extracted from the input image.
        status: The status of the code extraction process.
    """

    language: str = Field(
        ..., description="Programming language of the extracted code."
    )
    code: str = Field(..., description="The code extracted from the image.")
    status: str = Field(
        "completed", description="Status of the code extraction process."
    )

    @field_validator("language")
    @classmethod
    def lowercase_language(cls, v: str) -> str:
        return v.lower()


# Initialize OpenAI client with custom configuration
openai_client = AsyncOpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)

# Create instructor client for structured output generation
client = instructor.from_openai(
    openai_client,
    mode=instructor.Mode.JSON,
)

# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging and instrumentation for monitoring
logfire.configure(pydantic_plugin=logfire.PydanticPlugin(record="all"))
logfire.instrument_openai(openai_client)
logfire.instrument_fastapi(app)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(image: ImageInput) -> CodeOCRResponse:
    """Extract code from an image using a vision model.

    This endpoint processes images containing code from various sources (URLs,
    file paths, or base64-encoded data) and uses LLM's vision capabilities to
    extract the code content and automatically detect the programming language.
    The service leverages structured output generation to ensure consistent
    response formatting.

    Args:
        image: ImageInput model containing image source data that can be a URL,
               file path, or base64-encoded string.

    Returns:
        CodeOCRResponse containing the extracted code and detected programming language.

    Raises:
        HTTPException: If the image processing fails or the model cannot extract code.
        ValidationError: If the input image format is invalid.
    """
    response = await client.chat.completions.create(
        model=os.getenv("MODEL_NAME"),
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image",
                    Image.autodetect(image.source),
                ],
            },
        ],
    )
    return response


@app.post("/api/v1/codeocr/stream")
async def codeocr_stream(image: ImageInput):
    """Extract code from an image using streaming response.

    This endpoint provides the same functionality as the regular codeocr endpoint
    but returns the response as a Server-Sent Events (SSE) stream, allowing for
    real-time updates as the AI model generates the code extraction.

    Args:
        image: ImageInput model containing image source data that can be a URL,
               file path, or base64-encoded string.

    Returns:
        StreamingResponse with Server-Sent Events containing partial code extraction updates.
    """

    async def generate_stream():
        try:
            # Create partial streaming response
            extraction_stream = client.chat.completions.create_partial(
                model=os.getenv("MODEL_NAME"),
                response_model=CodeOCRResponse,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            "Extract code from this image",
                            Image.autodetect(image.source),
                        ],
                    },
                ],
                stream=True,
            )

            # Stream partial responses
            async for partial_response in extraction_stream:
                # Convert partial response to JSON
                data = {
                    "language": partial_response.language or "",
                    "code": partial_response.code or "",
                    "status": partial_response.status or "processing",
                }

                # Format as SSE event
                yield f"data: {json.dumps(data)}\n\n"

                # Small delay to make streaming visible
                await asyncio.sleep(0.1)

        except Exception as e:
            # Send error event
            error_data = {
                "language": "",
                "code": "",
                "status": "error",
                "error": str(e),
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        },
    )


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)

"use client";

import ImageUpload from "@/components/ImageUpload";
import StreamingCodeBlock from "@/components/StreamingCodeBlock";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2, Zap } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState, useRef } from "react";
import { toast } from "sonner";

interface CodeOCRResponse {
  language: string;
  code: string;
  status: string;
  error?: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
    // Close any existing EventSource
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
    setIsStreaming(false);
    // Close any existing EventSource
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
  }, []);

  const processImageStreaming = async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }

    setIsProcessing(true);
    setIsStreaming(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await fetch("/api/v1/codeocr/stream", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || errorData.detail || t("extractFailed")
        );
      }

      // Create EventSource for streaming
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data: CodeOCRResponse = JSON.parse(line.slice(6));
              setResult(data);

              if (data.status === "error") {
                throw new Error(data.error || t("extractFailed"));
              }

              if (data.status === "completed" || (data.code && data.language)) {
                setIsStreaming(false);
                toast.success(t("extractSuccess"));
              }
            } catch (parseError) {
              console.error("Error parsing SSE data:", parseError);
            }
          }
        }
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : t("extractFailed");
      toast.error(errorMessage);
      setIsStreaming(false);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4">
          <div className="space-y-4">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <Button
              onClick={processImageStreaming}
              disabled={!selectedFile || isProcessing}
              className="w-full"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isStreaming ? t("streaming") : t("processing")}
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  {t("extractCodeStreaming")}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {(result || isStreaming) && (
        <StreamingCodeBlock
          language={result?.language || ""}
          code={result?.code || ""}
          isStreaming={isStreaming}
          isComplete={
            result?.status === "completed" || (!isStreaming && !!result?.code)
          }
        />
      )}

      {/* No code message */}
      {result && !result.code && (
        <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
          No code to display
        </div>
      )}
    </div>
  );
}

"use client";

import { useEffect, useState } from "react";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  Code<PERSON>lock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockFiles,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Code } from "lucide-react";
import { useTranslations } from "next-intl";

interface StreamingCodeBlockProps {
  language: string;
  code: string;
  isStreaming: boolean;
  isComplete: boolean;
}

export default function StreamingCodeBlock({
  language,
  code,
  isStreaming,
  isComplete,
}: StreamingCodeBlockProps) {
  const t = useTranslations("CodeOCR");
  const [displayedCode, setDisplayedCode] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  // Reset when new streaming starts
  useEffect(() => {
    if (isStreaming && currentIndex === 0) {
      setDisplayedCode("");
      setCurrentIndex(0);
    }
  }, [isStreaming, currentIndex]);

  // Typewriter effect for streaming code
  useEffect(() => {
    if (!isStreaming || !code) {
      setDisplayedCode(code);
      return;
    }

    if (currentIndex < code.length) {
      const timer = setTimeout(() => {
        setDisplayedCode(code.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, 20); // Adjust speed here (lower = faster)

      return () => clearTimeout(timer);
    }
  }, [code, currentIndex, isStreaming]);

  // When streaming is complete, show full code immediately
  useEffect(() => {
    if (isComplete && code) {
      setDisplayedCode(code);
      setCurrentIndex(code.length);
    }
  }, [isComplete, code]);

  if (!code && !isStreaming) {
    return null;
  }

  const displayLanguage = language || "text";
  const filename = isStreaming 
    ? t("extractingCode") 
    : isComplete 
    ? t("extractedCode") 
    : t("extractedCode");

  return (
    <CodeBlock
      data={[
        {
          language: displayLanguage,
          filename: "",
          code: displayedCode,
        },
      ]}
      defaultValue={displayLanguage}
    >
      <CodeBlockHeader>
        <CodeBlockFiles>
          {(item) => (
            <CodeBlockFilename key={item.language} value={item.language}>
              <div className="flex items-center gap-2">
                <Code className={isStreaming ? "animate-pulse" : ""} />
                {filename}
                {isStreaming && (
                  <span className="text-xs text-muted-foreground animate-pulse">
                    ...
                  </span>
                )}
              </div>
            </CodeBlockFilename>
          )}
        </CodeBlockFiles>
        {displayedCode && <CodeBlockCopyButton />}
      </CodeBlockHeader>
      <CodeBlockBody>
        {(item) => (
          <CodeBlockItem key={item.language} value={item.language}>
            <CodeBlockContent language={item.language as BundledLanguage}>
              {item.code}
              {isStreaming && (
                <span className="animate-pulse text-muted-foreground">|</span>
              )}
            </CodeBlockContent>
          </CodeBlockItem>
        )}
      </CodeBlockBody>
    </CodeBlock>
  );
}
